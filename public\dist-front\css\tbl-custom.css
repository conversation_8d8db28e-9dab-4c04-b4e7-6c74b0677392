.whatsapp-float {
  position: fixed;
  width: 60px;
  height: 60px;
  bottom: 100px;
  right: 30px;
  background-color: #25d366;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: transform 0.3s ease, background-color 0.3s ease;
  animation: float 2s infinite ease-in-out;
}

/* تأثير عند تمرير الماوس */
.whatsapp-float:hover {
  background-color: #20b456;
  transform: scale(1.1);
  box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.4);
}

/* نمط الأيقونة */
.whatsapp-icon {
  font-size: 30px;
}

/* أنيميشن الأيقونة العائمة */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* my code */
.clients-grid div {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.clients-grid img {
  height: 100px; /* اضبط الارتفاع حسب الحاجة */
  object-fit: contain;
}

.clients-grid p {
  min-height: 40px; /* لضمان تناسق النصوص */
  margin-top: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: #4a5568;
}

<?php return array(
    'root' => array(
        'name' => 'laravel/laravel',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'brick/math' => array(
            'pretty_version' => '0.11.0',
            'version' => '0.11.0.0',
            'reference' => '0ad82ce168c82ba30d1c01ec86116ab52f589478',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cordoval/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'davedevelopment/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f41715465d65213d644d3141a6a93081be5d3549',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'reference' => 'f9301a5b2fb1216b2b08f02ba04dc45423db6bff',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '84a527db05647743d50373e0ec53a152f2cde568',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v3.3.3',
            'version' => '3.3.3.0',
            'reference' => 'adfb1f505deb6384dc8b39804c5065dd3c8c8c0a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.2',
            'version' => '4.0.2.0',
            'reference' => 'ebaaf5be6c0286928352e054f2d5125608e5405e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.16.0',
            'version' => '4.16.0.0',
            'reference' => '523407fb06eb9e5f3d59889b3978d5bfe94299c8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fakerphp/faker' => array(
            'pretty_version' => 'v1.23.0',
            'version' => '1.23.0.0',
            'reference' => 'e3daa170d00fde61ea7719ef47bb09bb8f1d9b01',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fakerphp/faker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'filp/whoops' => array(
            'pretty_version' => '2.15.3',
            'version' => '2.15.3.0',
            'reference' => 'c83e88a30524f9360b11f585f71e6b17313b7187',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filp/whoops',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'fruitcake/php-cors' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '3d158f36e7875e2f040f37bc0573956240a5a38b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fruitcake/php-cors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.1',
            'version' => '1.1.1.0',
            'reference' => '672eff8cf1d6fe1ef09ca0f89c4b287d6a3eb831',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.8.0',
            'version' => '7.8.0.0',
            'reference' => '1110f66a6530a40fe7aea0378fe608ee2b2248f9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '111166291a0f8130081195ac4556a5587d7f1b5d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.6.1',
            'version' => '2.6.1.0',
            'reference' => 'be45764272e8873c72dbe3d2edcfdfcc3bc9f727',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/uri-template' => array(
            'pretty_version' => 'v1.0.2',
            'version' => '1.0.2.0',
            'reference' => '61bf437fc2197f587f6857d3ff903a24f1731b5d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hamcrest/hamcrest-php' => array(
            'pretty_version' => 'v2.0.1',
            'version' => '2.0.1.0',
            'reference' => '8c3d0a3f6af734494ad8f6fbbee0ba92422859f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hamcrest/hamcrest-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/auth' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/broadcasting' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/bus' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/collections' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/conditionable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/config' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/container' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/contracts' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/cookie' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/events' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/hashing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/macroable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/mail' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/notifications' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/pagination' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/pipeline' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/process' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/queue' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/redis' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/routing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/session' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/support' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/testing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/translation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'illuminate/view' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v10.30.1',
            ),
        ),
        'kodova/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'laravel/framework' => array(
            'pretty_version' => 'v10.30.1',
            'version' => '10.30.1.0',
            'reference' => '7a2da50258c4d0f693b738d3f3c69b2693aea6c1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/laravel' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/pint' => array(
            'pretty_version' => 'v1.13.5',
            'version' => '1.13.5.0',
            'reference' => 'df105cf8ce7a8f0b8a9425ff45cd281a5448e423',
            'type' => 'project',
            'install_path' => __DIR__ . '/../laravel/pint',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.1.13',
            'version' => '0.1.13.0',
            'reference' => 'e1379d8ead15edd6cc4369c22274345982edc95a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/sail' => array(
            'pretty_version' => 'v1.26.0',
            'version' => '1.26.0.0',
            'reference' => 'c60fe037004e272efd0d81f416ed2bfc623d70b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sail',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/sanctum' => array(
            'pretty_version' => 'v3.3.1',
            'version' => '3.3.1.0',
            'reference' => '338f633e6487e76b255470d3373fbc29228aa971',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sanctum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v1.3.2',
            'version' => '1.3.2.0',
            'reference' => '076fe2cf128bd54b4341cdc6d49b95b34e101e4c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/tinker' => array(
            'pretty_version' => 'v2.8.2',
            'version' => '2.8.2.0',
            'reference' => 'b936d415b252b499e8c3b1f795cd4fc20f57e1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/tinker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/commonmark' => array(
            'pretty_version' => '2.4.1',
            'version' => '2.4.1.0',
            'reference' => '3669d6d5f7a47a93c08ddff335e6d945481a1dd5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/config' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => '754b3604fb2984c71f4af4a9cbe7b57f346ec1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.18.0',
            'version' => '3.18.0.0',
            'reference' => '015633a05aee22490495159237a5944091d8281e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.18.0',
            'version' => '3.18.0.0',
            'reference' => 'e7381ef7643f658b87efb7dbe98fe538fb1bbf32',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.14.0',
            'version' => '1.14.0.0',
            'reference' => 'b6a5854368533df0295c5761a0253656a2e52d9e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mews/purifier' => array(
            'pretty_version' => '3.4.1',
            'version' => '3.4.1.0',
            'reference' => '48e7bddc18140412d7cd815b6eea2fc7a141c432',
            'type' => 'package',
            'install_path' => __DIR__ . '/../mews/purifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mockery/mockery' => array(
            'pretty_version' => '1.6.6',
            'version' => '1.6.6.0',
            'reference' => 'b8e0bb7d8c604046539c1115994632c74dcb361e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mockery/mockery',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.5.0',
            'version' => '3.5.0.0',
            'reference' => 'c915e2634718dbc8a4a15c61b0e62e7a44e14448',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/cron-expression' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.0',
            ),
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.11.1',
            'version' => '1.11.1.0',
            'reference' => '7284c22080590fb39f2ffa3e9057f10a4ddd0e0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.71.0',
            'version' => '2.71.0.0',
            'reference' => '98276233188583f2ff845a0f992a235472d9466a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/schema' => array(
            'pretty_version' => 'v1.2.5',
            'version' => '1.2.5.0',
            'reference' => '0462f0166e823aad657c9224d0f849ecac1ba10a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.3',
            'version' => '4.0.3.0',
            'reference' => 'a9d127dd6a203ce6d255b2e2db49759f7506e015',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v4.17.1',
            'version' => '4.17.1.0',
            'reference' => 'a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/collision' => array(
            'pretty_version' => 'v7.10.0',
            'version' => '7.10.0.0',
            'reference' => '49ec67fa7b002712da8526678abd651c09f375b2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/collision',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v1.15.1',
            'version' => '1.15.1.0',
            'reference' => '8ab0b32c8caa4a2e09700ea32925441385e4a5dc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => '97803eca37d319dfa7826cc2437fc020857acb53',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.1',
            'version' => '1.9.1.0',
            'reference' => 'dd3a383e599f49777d8b628dadbb90cae435b87e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '10.1.7',
            'version' => '10.1.7.0',
            'reference' => '355324ca4980b8916c18b9db29f3ef484078f26e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '4.1.0',
            'version' => '4.1.0.0',
            'reference' => 'a95037b6d9e608ba092da1b23931e537cadc3c3c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => 'f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '0c7b06ff49e3d5072f057eb1fa59258bf287a748',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '6.0.0',
            'version' => '6.0.0.0',
            'reference' => 'e2a2d67966e740530f4a3343fe2e030ffdc1161d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '10.4.2',
            'version' => '10.4.2.0',
            'reference' => 'cacd8b9dd224efa8eb28beb69004126c7ca1a1a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'e616d01114759c4c489f93b099585439f795fe35',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'fe5ea303b0887d5caefd3d431c3e61ad47037001',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0.0',
                1 => '1.0|2.0|3.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.11.22',
            'version' => '0.11.22.0',
            'reference' => '128fa1b608be651999ed9789c95e6e2a31b5802b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.7.4',
            'version' => '4.7.4.0',
            'reference' => '60a4c63ab724854332900504274f6150ff26d286',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.7.4',
            ),
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'efdc130dbbbb8ef0b545a994fd811725c5282cae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a81fee9eef0b7a76af11d121767abc44c104e503',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '5e3a687f7d8ae33fb362c5c0743794bbb2420a1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '5.0.1',
            'version' => '5.0.1.0',
            'reference' => '2db5010a484d53ebf536087a70b4a5423c102372',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => '68cfb347a44871f01e33ab0ef8215966432f6957',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '5.0.3',
            'version' => '5.0.3.0',
            'reference' => '912dc2fbe3e3c1e7873313cc801b100b6c68c87b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '6.0.1',
            'version' => '6.0.1.0',
            'reference' => '43c751b41d74f96cbbd4e07b7aec9675651e2951',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '5.1.1',
            'version' => '5.1.1.0',
            'reference' => '64f51654862e0f5e318db7e9dcc2292c63cdbddc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '6.0.1',
            'version' => '6.0.1.0',
            'reference' => '7ea9ead78f6d380d2a667864c132c2f7b83055e4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '649e40d279e243d985aa8fb6e74dd5bb28dc185d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '5.0.0',
            'version' => '5.0.0.0',
            'reference' => '202d0e344a580d7f7d04b3fafce6933e59dae906',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '24ed13d98130f0e7122df55d06c5c4942a577957',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '5.0.0',
            'version' => '5.0.0.0',
            'reference' => '05909fb5bc7df4c52992396d0116aed689f93712',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => '462699a16464c3944eefc02ebdd77882bd3925bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'c51fa83a5d8f43f1402e3f32a005e6262244ef17',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/backtrace' => array(
            'pretty_version' => '1.5.3',
            'version' => '1.5.3.0',
            'reference' => '483f76a82964a0431aa836b6ed0edde0c248e3ab',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/backtrace',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/flare-client-php' => array(
            'pretty_version' => '1.4.3',
            'version' => '1.4.3.0',
            'reference' => '5db2fdd743c3ede33f2a5367d89ec1a7c9c1d1ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/flare-client-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/ignition' => array(
            'pretty_version' => '1.11.3',
            'version' => '1.11.3.0',
            'reference' => '3d886de644ff7a5b42e4d27c1e1f67c8b5f00044',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/ignition',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/laravel-ignition' => array(
            'pretty_version' => '2.3.1',
            'version' => '2.3.1.0',
            'reference' => 'bf21cd15aa47fa4ec5d73bbc932005c70261efc8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-ignition',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'stichoza/google-translate-php' => array(
            'pretty_version' => 'v5.1.3',
            'version' => '5.1.3.0',
            'reference' => '0d3de083a4b286868c4456cc76448a439a5aae07',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stichoza/google-translate-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v6.3.4',
            'version' => '6.3.4.0',
            'reference' => 'eca495f2ee845130855ddf1cf18460c38966c8b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v6.3.2',
            'version' => '6.3.2.0',
            'reference' => '883d961421ab1709877c10ac99451632a3d6fa57',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.3.0',
            'version' => '3.3.0.0',
            'reference' => '7c3aff79d10325257a001fcf92d991f24fc967cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v6.3.5',
            'version' => '6.3.5.0',
            'reference' => '1f69476b64fb47105c06beef757766c376b548c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.3.2',
            'version' => '6.3.2.0',
            'reference' => 'adb01fe097a4ee930db9258a3cc906b5beb5cf2e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.3.0',
            'version' => '3.3.0.0',
            'reference' => 'a76aed96a42d2b521153fb382d418e30d18b59df',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v6.3.5',
            'version' => '6.3.5.0',
            'reference' => 'a1b31d88c0e998168ca7792f222cbecee47428c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.3.7',
            'version' => '6.3.7.0',
            'reference' => '59d1837d5d992d16c2628cd0d6b76acf8d69b33e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v6.3.7',
            'version' => '6.3.7.0',
            'reference' => '6d4098095f93279d9536a0e9124439560cc764d0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v6.3.5',
            'version' => '6.3.5.0',
            'reference' => 'd89611a7830d51b5e118bca38e390dea92f9ea06',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.3.5',
            'version' => '6.3.5.0',
            'reference' => 'd5179eedf1cb2946dbd760475ebf05c251ef6a6e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => 'ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '875e90aeea2777b6f135677f618529449334a612',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => 'ecaafce9f77234a6a449d29e49267ba10499116d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '42292d99c55abe617799667f454222c54c60e229',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '70f4aebd92afca2f865444d30a4d2151c13c3179',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '6caa57379c4aec19c0a12a38b59b26487dcfe4b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => 'b0f46ebbeeeda3e9d2faebdfbf4b4eae9b59fa11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-uuid' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '9c44518a5aff8da565c8a55dbe85d2769e6f630e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v6.3.4',
            'version' => '6.3.4.0',
            'reference' => '0b5c29118f2e980d455d2e34a5659f4579847c54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v6.3.5',
            'version' => '6.3.5.0',
            'reference' => '82616e59acd3e3d9c916bba798326cb7796d7d31',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.3.0',
            'version' => '3.3.0.0',
            'reference' => '40da9cc13ec349d9e4966ce18b5fbcd724ab10a4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.3.5',
            'version' => '6.3.5.0',
            'reference' => '13d76d0fb049051ed12a04bef4f9de8715bea339',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v6.3.7',
            'version' => '6.3.7.0',
            'reference' => '30212e7c87dcb79c83f6362b00bde0e0b1213499',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.3.0',
            'version' => '3.3.0.0',
            'reference' => '02c24deb352fb0d79db5486c0c79905a85e37e86',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/uid' => array(
            'pretty_version' => 'v6.3.0',
            'version' => '6.3.0.0',
            'reference' => '01b0f20b1351d997711c56f1638f7a8c3061e384',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/uid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v6.3.6',
            'version' => '6.3.6.0',
            'reference' => '999ede244507c32b8e43aebaa10e9fce20de7c97',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v6.3.7',
            'version' => '6.3.7.0',
            'reference' => '9758b6c69d179936435d0ffb577c3708d57e38a8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => '34a41e998c2183e22995f158c581e7b5e755ab9e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => '2.2.6',
            'version' => '2.2.6.0',
            'reference' => 'c42125b83a4fa63b187fdf29f9c93cb7733da30c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.5.0',
            'version' => '5.5.0.0',
            'reference' => '1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'b56450eed252f6801410d810c8e1727224ae0743',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
